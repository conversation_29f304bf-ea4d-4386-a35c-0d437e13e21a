<head>
  <link
    href="https://fonts.googleapis.com/css2?family=Space+Mono:ital,wght@0,400;0,700;1,400;1,700&family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200&display=block"
    rel="stylesheet"
  />
  <script type="importmap">
    {
      "imports": {
        "@google/genai": "https://esm.sh/@google/genai@^0.7.0",
        "react": "https://esm.sh/react@^19.0.0",
        "react/": "https://esm.sh/react@^19.0.0/",
        "react-dom/": "https://esm.sh/react-dom@^19.0.0/",
        "d3-array": "https://esm.sh/d3-array@^3.2.4",
        "d3-shape": "https://esm.sh/d3-shape@^3.2.0",
        "classnames": "https://esm.sh/classnames@^2.5.1",
        "@google/generative-ai": "https://esm.sh/@google/generative-ai@^0.24.0",
        "d3-scale": "https://esm.sh/d3-scale@^4.0.2"
      }
    }
  </script>
<link rel="stylesheet" href="/index.css">
</head>
<div id="root"></div>
<script type="module" src="/index.tsx"></script>
